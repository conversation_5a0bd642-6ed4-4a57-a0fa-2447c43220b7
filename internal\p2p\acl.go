package p2p

import (
	"log"
	"time"

	"github.com/libp2p/go-libp2p/core/network"
	"github.com/libp2p/go-libp2p/core/peer"
	"github.com/multiformats/go-multiaddr"
)

// LoggingACL is an ACLFilter that logs requests and allows them.
type LoggingACL struct{}

// AllowReserve logs and allows a reservation.
func (a *LoggingACL) AllowReserve(p peer.ID, addr multiaddr.Multiaddr) bool {
	log.Printf("📍 [中继预留] 时间: %s", time.Now().Format("2006-01-02 15:04:05"))
	log.Printf("📍 [中继预留] 节点ID: %s", p.String())
	log.Printf("📍 [中继预留] 节点地址: %s", addr.String())
	log.Printf("📍 [中继预留] 状态: ✅ 允许预留中继槽位")
	log.Printf("📍 [中继预留] ---")
	return true
}

// AllowConnect logs and allows a connection.
func (a *LoggingACL) AllowConnect(src peer.ID, srcAddr multiaddr.Multiaddr, dest peer.ID) bool {
	log.Printf("🔗 [中继连接] 时间: %s", time.Now().Format("2006-01-02 15:04:05"))
	log.Printf("🔗 [中继连接] 源节点ID: %s", src.String())
	log.Printf("🔗 [中继连接] 源节点地址: %s", srcAddr.String())
	log.Printf("🔗 [中继连接] 目标节点ID: %s", dest.String())
	log.Printf("🔗 [中继连接] 状态: ✅ 允许中继连接")
	log.Printf("🔗 [中继连接] ---")
	return true
}

// NetworkNotifiee logs network events
type NetworkNotifiee struct{}

// Listen logs when we start listening on a multiaddr
func (n *NetworkNotifiee) Listen(net network.Network, addr multiaddr.Multiaddr) {
	// This is called when we start listening
}

// ListenClose logs when we stop listening on a multiaddr
func (n *NetworkNotifiee) ListenClose(net network.Network, addr multiaddr.Multiaddr) {
	// This is called when we stop listening
}

// Connected logs when a peer connects
func (n *NetworkNotifiee) Connected(net network.Network, conn network.Conn) {
	log.Printf("🔌 [节点连接] 时间: %s", time.Now().Format("2006-01-02 15:04:05"))
	log.Printf("🔌 [节点连接] 远程节点ID: %s", conn.RemotePeer().String())
	log.Printf("🔌 [节点连接] 远程地址: %s", conn.RemoteMultiaddr().String())
	log.Printf("🔌 [节点连接] 本地地址: %s", conn.LocalMultiaddr().String())
	log.Printf("🔌 [节点连接] 状态: ✅ 节点已连接到服务器")
	log.Printf("🔌 [节点连接] ---")
}

// Disconnected logs when a peer disconnects
func (n *NetworkNotifiee) Disconnected(net network.Network, conn network.Conn) {
	log.Printf("🔌 [节点断开] 时间: %s", time.Now().Format("2006-01-02 15:04:05"))
	log.Printf("🔌 [节点断开] 远程节点ID: %s", conn.RemotePeer().String())
	log.Printf("🔌 [节点断开] 远程地址: %s", conn.RemoteMultiaddr().String())
	log.Printf("🔌 [节点断开] 状态: ❌ 节点已断开连接")
	log.Printf("🔌 [节点断开] ---")
}
