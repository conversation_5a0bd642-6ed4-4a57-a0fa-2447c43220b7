package p2p

import (
	"crypto/rand"
	"fmt"
	"io/ioutil"
	"os"

	"github.com/libp2p/go-libp2p/core/crypto"
)

// LoadOrGeneratePrivateKey 从给定路径加载一个私钥。
// 如果文件不存在，它会生成一个新的私钥，并将其保存到该路径以供将来使用。
func LoadOrGeneratePrivateKey(path string) (crypto.PrivKey, error) {
	if _, err := os.Stat(path); os.IsNotExist(err) {
		// 文件不存在，生成新密钥
		priv, _, err := crypto.GenerateKeyPairWithReader(crypto.Ed25519, 2048, rand.Reader)
		if err != nil {
			return nil, fmt.Errorf("生成密钥对失败: %w", err)
		}

		// 将密钥保存到文件
		keyBytes, err := crypto.MarshalPrivateKey(priv)
		if err != nil {
			return nil, fmt.Errorf("序列化私钥失败: %w", err)
		}

		if err := ioutil.WriteFile(path, keyBytes, 0600); err != nil {
			return nil, fmt.Errorf("保存私钥到文件失败: %w", err)
		}
		fmt.Printf("🔑 在 %s 生成并保存了新的私钥\n", path)
		return priv, nil
	}

	// 文件存在，加载密钥
	keyBytes, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("从文件读取私钥失败: %w", err)
	}

	priv, err := crypto.UnmarshalPrivateKey(keyBytes)
	if err != nil {
		return nil, fmt.Errorf("反序列化私钥失败: %w", err)
	}
	fmt.Printf("🔑 从 %s 加载了现有私钥\n", path)
	return priv, nil
}
