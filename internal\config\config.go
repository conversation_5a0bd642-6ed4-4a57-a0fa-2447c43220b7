package config

import (
	"github.com/spf13/viper"
)

// Forward represents a single port forwarding rule.
type Forward struct {
	LocalPort    int    `mapstructure:"local_port"`
	RemoteTarget string `mapstructure:"remote_target"`
}

// Config holds the application configuration.
type Config struct {
	Signal   string    `mapstructure:"signal"`
	Peer     string    `mapstructure:"peer"`
	Forwards []Forward `mapstructure:"forwards"`
}

// InitConfig initializes viper to load configuration.
func InitConfig(cfgFile string) error {
	if cfgFile != "" {
		viper.SetConfigFile(cfgFile)
	} else {
		viper.AddConfigPath(".")
		viper.SetConfigName("config")
	}

	viper.SetConfigType("yaml")
	viper.AutomaticEnv() // read in environment variables that match

	// If a config file is found, read it in.
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			// Config file was found but another error was produced
			return err
		}
		// Config file not found; ignore error
	}
	return nil
}

// GetConfig unmarshals the loaded config into our struct.
func GetConfig() (Config, error) {
	var config Config
	err := viper.Unmarshal(&config)
	return config, err
}
