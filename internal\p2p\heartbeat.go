package p2p

import (
    "context"
    "log"
    "time"

    "github.com/libp2p/go-libp2p/core/host"
    "github.com/libp2p/go-libp2p/core/peer"
    "github.com/libp2p/go-libp2p/core/protocol"
)

const HeartbeatProtocolID = protocol.ID("/peertunnel/heartbeat/1.0.0")
const HeartbeatInterval = 15 * time.Second

// StartHeartbeat 启动心跳机制以保持连接活跃
func StartHeartbeat(ctx context.Context, h host.Host, remotePeer peer.ID) {
    go func() {
        ticker := time.NewTicker(HeartbeatInterval)
        defer ticker.Stop()

        for {
            select {
            case <-ticker.C:
                if err := sendHeartbeat(ctx, h, remotePeer); err != nil {
                    log.Printf("❤️ [心跳] 发送心跳失败: %v", err)
                }
            case <-ctx.Done():
                log.Printf("❤️ [心跳] 心跳服务已停止")
                return
            }
        }
    }()
}

func sendHeartbeat(ctx context.Context, h host.Host, remotePeer peer.ID) error {
    // 创建一个短超时的上下文
    beatCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()

    stream, err := h.NewStream(beatCtx, remotePeer, HeartbeatProtocolID)
    if err != nil {
        return err
    }
    defer stream.Close()

    // 简单地发送一个字节作为心跳
    _, err = stream.Write([]byte{1})
    if err != nil {
        return err
    }

    log.Printf("❤️ [心跳] 成功发送心跳到 %s", remotePeer.String())
    return nil
}

// 设置心跳处理器
func SetupHeartbeatHandler(h host.Host) {
    h.SetStreamHandler(HeartbeatProtocolID, func(stream network.Stream) {
        // 简单地读取并丢弃心跳数据
        buf := make([]byte, 1)
        _, err := stream.Read(buf)
        if err != nil {
            log.Printf("❤️ [心跳] 读取心跳失败: %v", err)
        }
        stream.Close()
    })
}