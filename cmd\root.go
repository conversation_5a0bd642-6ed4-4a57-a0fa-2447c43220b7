package cmd

import (
	"fmt"
	"os"

	"github.com/47sang/peertunnel/internal/config"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var (
	cfgFile string
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "peertunnel",
	Short: "PeerTunnel 是一个多路复用的P2P隧道工具。",
	Long: `PeerTunnel 创建一个多路复用的P2P隧道，允许您通过单个连接安全地、
并发地访问远程节点上的多个服务。`,
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func init() {
	cobra.OnInitialize(initConfig)
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "配置文件 (默认为 ./config.yaml)")
	rootCmd.PersistentFlags().String("signal", "", "信令服务器地址 (覆盖配置)")
	viper.BindPFlag("signal", rootCmd.PersistentFlags().Lookup("signal"))
}

func initConfig() {
	if err := config.InitConfig(cfgFile); err != nil {
		fmt.Fprintf(os.Stderr, "初始化配置时出错: %v\n", err)
		os.Exit(1)
	}
}
