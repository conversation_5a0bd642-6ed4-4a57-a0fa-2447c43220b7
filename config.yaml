# 信令服务器地址
# 这是所有PeerTunnel节点用来发现彼此的"集合点"
# 如果您自己部署了信令服务器，请替换成您的服务器地址
# 格式: /ip4/<你的公网IP>/tcp/<端口号>
signal: "/ip4/************/tcp/4001/p2p/12D3KooWGRiSRZh5eJGLBzR2SCP8Addh3FctM1XgNqVs8f9enEPo"

# ---- 连接方 (Connector) 配置 ----
# 目标监听方的Peer ID
# 当您作为连接方时，需要填写监听方的Peer ID
# 监听方启动时会在日志中打印出自己的Peer ID
peer: "12D3KooWN5Sg4o9xkaqpeCgEib7NeqaKk6Yr5wftkwAt8UQKeDjZ" 

# 核心: 定义一个或多个转发映射
# 这是v3.0的核心功能，允许您通过一个P2P连接访问多个远程服务
forwards:
  # 规则一: 代理远程的Web服务到本地的8080端口
  - local_port: 11668
    remote_target: "127.0.0.1:3306"
  
  # 规则二: 代理远程的数据库到本地的5433端口
  - local_port: 11667
    remote_target: "127.0.0.1:22"

  # 规则三: 代理远程的SSH服务到本地的2222端口
  - local_port: 11666
    remote_target: "127.0.0.1:902" 