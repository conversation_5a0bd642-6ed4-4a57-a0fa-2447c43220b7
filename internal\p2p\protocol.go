package p2p

import (
	"bufio"
	"encoding/json"
	"fmt"

	"github.com/libp2p/go-libp2p/core/network"
)

const (
	// ControlProtocolID is the protocol ID for control messages
	ControlProtocolID = "/peertunnel/control/1.0.0"
	// ProxyProtocolID is the protocol ID for proxying data
	ProxyProtocolID = "/peertunnel/proxy/1.0.0"
)

// ControlMessage is used to request a new proxy stream.
type ControlMessage struct {
	// RequestID is a unique identifier for this proxy request.
	RequestID string `json:"request_id"`
	// RemoteTarget is the address of the service the listener should connect to.
	RemoteTarget string `json:"remote_target"`
}

// WriteControlMessage sends a control message to the given stream.
func WriteControlMessage(stream network.Stream, msg ControlMessage) error {
	writer := bufio.NewWriter(stream)
	encoder := json.NewEncoder(writer)
	if err := encoder.Encode(msg); err != nil {
		return fmt.Errorf("编码控制消息失败: %w", err)
	}
	if err := writer.Flush(); err != nil {
		return fmt.Errorf("刷新控制消息失败: %w", err)
	}
	return nil
}

// ReadControlMessage reads a control message from the given stream.
func ReadControlMessage(stream network.Stream) (*ControlMessage, error) {
	var msg ControlMessage
	reader := bufio.NewReader(stream)
	decoder := json.NewDecoder(reader)
	if err := decoder.Decode(&msg); err != nil {
		return nil, fmt.Errorf("解码控制消息失败: %w", err)
	}
	return &msg, nil
}
