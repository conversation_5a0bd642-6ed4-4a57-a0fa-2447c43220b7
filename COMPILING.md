# PeerTunnel 编译指南

本文档提供了在不同操作系统和架构上编译 `PeerTunnel` 项目的详细命令。

## 1. 准备工作

在开始编译之前，请确保您已经安装了 [Go 语言环境](https://go.dev/dl/) (建议版本 1.18 或更高)。

同时，请确保项目依赖已同步。在项目根目录下运行：

```powershell
go mod tidy
```

## 2. 本地编译

此命令将在您当前的操作系统和架构下生成一个可执行文件。

### 在 Windows 上 (PowerShell)

```powershell
go build -o peertunnel.exe ./cmd/peertunnel
```

### 在 Linux 或 macOS 上 (Bash/Zsh)

```bash
go build -o peertunnel ./cmd/peertunnel
```

## 3. 交叉编译

Go 语言的工具链可以轻松地从单一平台生成适用于其他平台的可执行文件。这通过设置 `GOOS` (目标操作系统) 和 `GOARCH` (目标架构) 环境变量来实现。

**注意:** 以下命令均针对在 **PowerShell (Windows)** 环境下执行。如果您在 Linux 或 macOS 上进行交叉编译，请使用 `export GOOS=...` 或直接在命令前加上 `GOOS=... GOARCH=...`。

---

### 编译到 Windows

```powershell
# 目标: Windows 64-bit (amd64)
$env:GOOS="windows"; $env:GOARCH="amd64"; go build -o peertunnel_windows_amd64.exe ./cmd/peertunnel
```

---

### 编译到 Linux

```powershell
# 目标: Linux 64-bit (amd64)
$env:GOOS="linux"; $env:GOARCH="amd64"; go build -o peertunnel_linux_amd64 ./cmd/peertunnel

# 目标: Linux 64-bit (arm64, 例如 AWS Graviton, Raspberry Pi 4)
$env:GOOS="linux"; $env:GOARCH="arm64"; go build -o peertunnel_linux_arm64 ./cmd/peertunnel
```

---

### 编译到 macOS

```powershell
# 目标: macOS 64-bit (Intel)
$env:GOOS="darwin"; $env:GOARCH="amd64"; go build -o peertunnel_macos_amd64 ./cmd/peertunnel

# 目标: macOS 64-bit (Apple Silicon, M1/M2/M3)
$env:GOOS="darwin"; $env:GOARCH="arm64"; go build -o peertunnel_macos_arm64 ./cmd/peertunnel
```

---

### 如何在 Linux/macOS 上交叉编译 (Bash/Zsh 示例)

如果您切换到 Linux 或 macOS 环境，交叉编译的语法会略有不同。示例如下：

```bash
# 编译到 Windows 64-bit
GOOS=windows GOARCH=amd64 go build -o peertunnel.exe ./cmd/peertunnel

# 编译到 Linux arm64
GOOS=linux GOARCH=arm64 go build -o peertunnel_linux_arm64 ./cmd/peertunnel
```

在执行完编译命令后，您会在项目根目录下找到相应的可执行文件。

## 4. 一次性编译多个平台 (PowerShell 示例)

以下命令展示了如何在一个命令中同时编译 Windows 和 Linux 的 amd64 版本：

```powershell
# 同时编译 Windows (amd64) 和 Linux (amd64)
$env:GOOS="linux"; $env:GOARCH="amd64"; go build -o peertunnel ./cmd/peertunnel; $env:GOOS="windows"; $env:GOARCH="amd64"; go build -o peertunnel.exe ./cmd/peertunnel
``` 