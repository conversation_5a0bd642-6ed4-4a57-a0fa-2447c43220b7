package cmd

import (
	"bufio"
	"context"
	"fmt"
	"log"
	"net"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/47sang/peertunnel/internal/config"
	"github.com/47sang/peertunnel/internal/p2p"
	"github.com/libp2p/go-libp2p/core/host"
	"github.com/libp2p/go-libp2p/core/network"
	"github.com/libp2p/go-libp2p/core/peer"
	"github.com/multiformats/go-multiaddr"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var connectCmd = &cobra.Command{
	Use:   "connect",
	Short: "连接到监听器并创建转发隧道",
	Long:  `连接到监听器节点，并根据配置文件建立多个隧道。`,
	Run: func(cmd *cobra.Command, args []string) {
		cfg, err := config.GetConfig()
		if err != nil {
			log.Fatalf("获取配置失败: %v", err)
		}

		signalAddr := viper.GetString("signal")
		if signalAddr == "" {
			signalAddr = cfg.Signal
		}
		peerIDStr := viper.GetString("peer")
		if peerIDStr == "" {
			peerIDStr = cfg.Peer
		}

		if signalAddr == "" || peerIDStr == "" {
			log.Fatal("必须同时提供信令服务器地址和节点ID。")
		}

		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		var staticRelays []multiaddr.Multiaddr
		if signalAddr != "" {
			addr, err := multiaddr.NewMultiaddr(signalAddr)
			if err != nil {
				log.Fatalf("无效的信令服务器地址: %v", err)
			}
			staticRelays = append(staticRelays, addr)
		}

		h, err := p2p.NewHost(ctx, 0, false, staticRelays, nil)
		if err != nil {
			log.Fatalf("创建主机失败: %v", err)
		}

		log.Printf("连接器已启动，节点ID: %s", h.ID().String())

		if err := p2p.ConnectToPeer(ctx, h, signalAddr); err != nil {
			log.Fatalf("连接到信令服务器失败: %v", err)
		}

		targetPeerID, err := peer.Decode(peerIDStr)
		if err != nil {
			log.Fatalf("解码节点ID失败: %v", err)
		}

		relayAddr, err := multiaddr.NewMultiaddr(fmt.Sprintf("%s/p2p-circuit/p2p/%s", signalAddr, targetPeerID.String()))
		if err != nil {
			log.Fatalf("创建中继地址失败: %v", err)
		}

		log.Printf("尝试通过中继连接到监听器: %s", targetPeerID.String())
		log.Printf("中继地址: %s", relayAddr.String())

		// Try to connect with retries
		var connectErr error
		maxRetries := 3
		for retry := 0; retry < maxRetries; retry++ {
			if retry > 0 {
				log.Printf("连接重试 %d/%d...", retry+1, maxRetries)
				time.Sleep(time.Duration(retry*2) * time.Second)
			}

			connectErr = h.Connect(ctx, peer.AddrInfo{ID: targetPeerID, Addrs: []multiaddr.Multiaddr{relayAddr}})
			if connectErr == nil {
				break
			}

			log.Printf("连接尝试 %d 失败: %v", retry+1, connectErr)
		}

		if connectErr != nil {
			log.Println("❌ 所有连接尝试都失败了。")
			log.Println("💡 可能的原因：")
			log.Println("   1. 监听器没有成功预留中继槽位")
			log.Println("   2. 监听器节点ID不正确")
			log.Println("   3. 信令服务器中继服务未启用")
			log.Println("   4. 网络连接问题")
			log.Printf("   最后一次错误: %v", connectErr)
			log.Fatalf("连接到监听器失败: %v", connectErr)
		}
		log.Printf("成功连接到监听器: %s", targetPeerID.String())

		streamManager := p2p.NewStreamManager()
		go func() {
			for {
				time.Sleep(30 * time.Second)
				streamManager.Cleanup(1 * time.Minute)
			}
		}()

		h.SetStreamHandler(p2p.ProxyProtocolID, proxyStreamHandler(streamManager))

		var wg sync.WaitGroup
		for _, f := range cfg.Forwards {
			wg.Add(1)
			go func(forward config.Forward) {
				defer wg.Done()
				startLocalListener(ctx, h, targetPeerID, forward, streamManager)
			}(f)
		}

		// Block until shutdown
		ch := make(chan os.Signal, 1)
		signal.Notify(ch, syscall.SIGINT, syscall.SIGTERM)
		<-ch
		fmt.Println("\n收到信号，正在关闭...")
		cancel()
		// Allow some time for goroutines to clean up.
		time.Sleep(1 * time.Second)
	},
}

func proxyStreamHandler(manager *p2p.StreamManager) network.StreamHandler {
	return func(stream network.Stream) {
		log.Println("从监听器收到新的代理流")
		reader := bufio.NewReader(stream)
		requestID, err := reader.ReadString('\n')
		if err != nil {
			log.Printf("从代理流读取请求ID失败: %v", err)
			stream.Reset()
			return
		}
		requestID = requestID[:len(requestID)-1] // Trim newline

		if !manager.FulfillRequest(requestID, stream) {
			log.Printf("未找到ID为 %s 的待处理请求。正在关闭流。", requestID)
			stream.Reset()
		}
	}
}

func startLocalListener(ctx context.Context, h host.Host, targetPeerID peer.ID, forward config.Forward, manager *p2p.StreamManager) {
	localAddr := fmt.Sprintf("127.0.0.1:%d", forward.LocalPort)
	listener, err := net.Listen("tcp", localAddr)
	if err != nil {
		log.Fatalf("在 %s 上监听失败: %v", localAddr, err)
	}
	defer listener.Close()

	log.Printf("正在 %s 上监听，转发到远程 %s", localAddr, forward.RemoteTarget)

	for {
		select {
		case <-ctx.Done():
			return
		default:
		}

		localConn, err := listener.Accept()
		if err != nil {
			log.Printf("在 %s 上接受连接失败: %v", localAddr, err)
			continue
		}

		go func(localConn net.Conn) {
			defer localConn.Close()
			log.Printf("为 %s 建立新本地连接", localAddr)

			requestID, streamChan := manager.RegisterRequest()

			var controlStream network.Stream
			var err error
			// 为 NewStream 添加重试逻辑
			for i := 0; i < 3; i++ {
				// 每次循环都创建一个新的带超时的上下文
				streamCtx, cancel := context.WithTimeout(ctx, 8*time.Second)

				controlStream, err = h.NewStream(streamCtx, targetPeerID, p2p.ControlProtocolID)
				cancel() // 在尝试后立即调用 cancel，以释放与该上下文关联的资源

				if err == nil {
					break // 成功，跳出循环
				}
				log.Printf("[ID: %s] 打开控制流失败 (尝试 %d/3): %v", requestID, i+1, err)

				// 在重试前等待，或者如果主上下文被取消则退出
				select {
				case <-ctx.Done():
					return
				case <-time.After(time.Duration(i+1) * 2 * time.Second): // 退避等待
				}
			}

			if err != nil {
				log.Printf("[ID: %s] 打开控制流最终失败: %v", requestID, err)
				return
			}

			msg := p2p.ControlMessage{RequestID: requestID, RemoteTarget: forward.RemoteTarget}
			if err := p2p.WriteControlMessage(controlStream, msg); err != nil {
				log.Printf("[ID: %s] 发送控制消息失败: %v", requestID, err)
				controlStream.Reset()
				return
			}
			controlStream.Close()

			log.Printf("[ID: %s] 控制消息已发送。等待代理流...", requestID)

			select {
			case proxyStream, ok := <-streamChan:
				if !ok {
					log.Printf("[ID: %s] 请求超时或已取消。", requestID)
					return
				}
				log.Printf("[ID: %s] 代理流已收到。开始代理数据。", requestID)
				p2p.Proxy(proxyStream, localConn)
			case <-time.After(10 * time.Second):
				log.Printf("[ID: %s] 等待代理流超时。", requestID)
			case <-ctx.Done():
				return
			}
		}(localConn)
	}
}

func init() {
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	rootCmd.AddCommand(connectCmd)
	connectCmd.PersistentFlags().String("peer", "", "监听器的节点ID")
	viper.BindPFlag("peer", connectCmd.PersistentFlags().Lookup("peer"))
}
