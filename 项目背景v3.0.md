好的，我完全理解这个需求。这是对 v2.0 的一个巨大飞跃，将 `PeerTunnel` 从“一次连接一个隧道”的模式，提升为“一次连接，多个隧道并行”的模式。这极大地提高了效率和便利性，让它更像一个轻量级的、点对点的“服务网格”入口。

下面是根据这个最新、最强大的需求，为你生成的终版项目背景文档。

---

### **项目背景文档：PeerTunnel - 多路复用P2P隧道及动态服务网关CLI工具**

**版本:** 3.0
**日期:** 2025年06月06日
**创建人:** 47sang

---

### 1. 项目概述 (Executive Summary)

**PeerTunnel** 是一个基于Go语言开发的命令行工具（CLI），它构建了一个**多路复用**的P2P隧道，允许用户通过单次连接，即可安全、并行地访问远程节点上的多个不同服务。项目的核心架构是“连接方驱动”，监听方（Listener）作为一个通用的P2P网关节点运行。连接方（Connector）通过一份配置文件或命令行参数，定义一组“本地端口”到“远程目标服务”的映射规则。

当连接方启动时，它会与监听方建立一个持久的P2P会话。在这个会话内，`PeerTunnel`会根据配置的每一条映射规则，动态地创建独立的、加密的子流（sub-stream）。这意味着用户可以在一次启动后，同时访问远程节点的Web服务、数据库、SSH等，而无需任何切换操作。这种设计为远程开发、DevOps、IoT设备集群管理等场景，提供了一个极致高效、配置简单且高度安全的统一访问解决方案。

### 2. 项目背景与核心痛点 (Background and Pain Points)

现代软件开发与运维工作流常常需要同时与多个后端服务交互。`PeerTunnel` v3.0旨在解决以下高级场景中的核心痛点：

*   **串行操作的低效:** 传统的隧道工具要求用户为每一个需要访问的远程服务建立一个独立的连接。在调试一个需要同时访问API、数据库和缓存的复杂应用时，这种反复断开、重连、切换配置的操作流程极其繁琐且浪费时间。
*   **资源与端口的浪费:** 为每个远程服务在本地开启一个隧道进程，不仅消耗了更多的系统资源，也占用了大量的本地端口，容易造成端口冲突和管理混乱。
*   **缺乏统一的访问视图:** 用户需要记忆或管理多个本地端口分别对应哪个远程服务，缺乏一个集中的配置来定义和管理所有远程服务的访问入口。
*   **全栈开发的割裂感:** 前端开发者在本地开发时，需要模拟一个完整的后端环境。`PeerTunnel`能够将远程开发环境中的所有相关服务（API, Mock Server, Auth Service等）一次性地、透明地映射到本地，创造出无缝的“本地化”开发体验。
*   **复杂的远程环境管理:** 管理员需要对远程服务器或IoT设备进行维护时，可能需要同时使用SSH、访问Web管理后台、查看日志流等。`PeerTunnel`通过一次连接即可暴露所有必要的管理端口。

### 3. 项目目标与范围 (Goals and Scope)

#### **3.1 项目目标**

*   **实现多路复用隧道:** 在单个P2P连接上支持并发传输多个独立的、隔离的子流，每个子流对应一个代理的服务。
*   **支持多服务映射配置:** 允许连接方在配置文件或命令行中定义一个列表，包含多条从“本地端口”到“远程目标服务”的转发规则。
*   **按需动态建立子流:** 当有流量访问连接方的某个本地代理端口时，才通过P2P主会话向监听方请求建立对应的服务子流。
*   **提供统一的配置管理:** 通过优雅的配置文件格式（如YAML），让用户能够清晰地管理一组服务映射，并轻松地在不同工作集之间切换。
*   **保障端到端安全:** 整个P2P会话以及其上的所有子流都必须经过强加密，确保所有控制信令和业务数据的机密性。
*   **实现高可靠连接:** 依赖公网信令服务器完成节点发现和NAT穿透，并在必要时提供流量中继能力。

#### **3.2 项目范围 (In-Scope)**

*   **PeerTunnel CLI 客户端:**
    *   **配置文件增强:** 支持定义一个`forwards`列表，每个元素包含`local_port`和`remote_target`。
    *   **多路复用协议:** 在`libp2p`的主连接之上，设计一个应用层协议，用于管理和协商多个子流的建立。例如，当本地`9001`端口有流量时，通过主连接发送指令，要求对方为`localhost:5432`服务创建一个新的子流。
*   **信令服务器 (Rendezvous Server):**
    *   一个独立的Go应用，部署于公网，负责节点注册、发现和可选的流量中继。
*   **P2P连接与安全:**
    *   使用`go-libp2p`库，利用其内置的流多路复用能力（如mplex或yamux）和加密通道（如Noise）。
*   **反向代理功能:**
    *   连接方能够同时在多个本地端口上启动监听。
    *   监听方能够根据收到的指令，并发地将不同子流的流量转发到不同的本地目标服务。

#### **3.3 非项目范围 (Out-of-Scope for v3.0)**

*   图形用户界面（GUI）。
*   动态修改已建立连接的转发规则（需重启连接方以加载新配置）。
*   负载均衡或服务发现等高级服务网格功能。

### 4. 系统架构与多路复用工作流程

系统架构依然是**信令服务器**和**PeerTunnel CLI客户端**，但其内部交互模型变得更为复杂和强大。

#### **4.1 组件说明**

1.  **信令服务器 (Rendezvous Server):** 职责保持为P2P连接的“介绍人”和“备用中转站”。

2.  **PeerTunnel CLI (客户端):**
    *   **监听方 (Listener) / "服务网关":**
        *   **启动与待命:** 启动后连接信令服务器注册自己，等待P2P连接请求。
        *   **协商主会话:** 与连接方建立一个P2P主连接。
        *   **动态响应子流请求:** 在主连接上监听来自连接方的“子流创建请求”。每个请求都包含一个远程目标地址（如`localhost:8080`）。
        *   **并发代理:** 对于每一个成功的子流请求，它都会打开一个新的P2P子流，并将该子流的数据定向到其本地对应的目标服务。它可以同时处理多个到不同服务的代理会话。
    *   **连接方 (Connector) / "访问入口":**
        *   **加载多规则配置:** 启动时，从配置文件或命令行加载一组转发规则。
        *   **建立主会话:** 连接信令服务器，找到监听方并建立一个P2P主连接。
        *   **启动多端口监听:** 根据配置，在本地启动多个TCP监听器（如`localhost:9000`, `localhost:9001`等）。
        *   **按需发起子流:** 当某个本地端口（如`9000`）接收到第一个连接时，它会通过P2P主连接向监听方发送一个“子流创建请求”，指明此流量需要转发到对应的远程目标（如`localhost:8080`）。
        *   **数据转发:** 一旦子流建立，该本地端口上的所有后续流量都将通过这个专用的子流进行传输。

#### **4.2 多路复用工作流程示例**

**场景：全栈开发者小李（连接方）需要同时访问小王（监听方）电脑上的Web API、数据库和Redis缓存。**

1.  **准备:**
    *   小王启动监听方，进入待命状态：`peertunnel listen --config ./config_wang.yaml`
    *   小李在他的电脑上创建配置文件 `config_li.yaml`：
      ```yaml
      signal: /ip4/your-server.com/tcp/4001
      peer: <小王的PeerID>
      
      # 定义一组转发规则
      forwards:
        - local_port: 9000
          remote_target: localhost:8080 # Web API
        - local_port: 9001
          remote_target: localhost:5432 # PostgreSQL DB
        - local_port: 9002
          remote_target: localhost:6379 # Redis
      ```

2.  **一次性启动连接:**
    *   小李启动连接方: `peertunnel connect`
    *   **流程:**
        a. 小李的客户端解析配置，并在本地同时监听 `9000`, `9001`, `9002` 端口。
        b. 一个持久化的、加密的P2P主会话在小李和小王之间建立。

3.  **并行访问多个服务:**
    *   **访问Web API:** 小李在浏览器中打开 `http://localhost:9000`。
        *   这是`9000`端口的首次流量。小李的客户端通过主会话发送请求：“请为`localhost:8080`创建一个子流”。
        *   小王的客户端响应请求，建立一个专用于Web API的子流。
        *   HTTP请求和响应通过这个新子流进行传输。
    *   **访问数据库:** 同时，小李的后端服务尝试连接数据库 `postgres://user:pass@localhost:9001`。
        *   这是`9001`端口的首次流量。小李的客户端通过主会话发送另一个请求：“请为`localhost:5432`创建一个子流”。
        *   小王的客户端响应，建立一个专用于数据库的、与Web API子流并行的子流。
        *   数据库查询通过这个新的子流进行传输。
    *   **访问Redis:** 程序的其他部分连接到 `redis://localhost:9002`，触发第三个子流的创建。

4.  **结果:**
    *   小李只需启动一次`peertunnel`，就能将远程的整个开发环境无缝地映射到本地。所有三个服务可以同时、独立地被访问，互不干扰。关闭`peertunnel`进程，所有隧道同时断开。

### 5. 技术选型

*   **编程语言:** **Go**
*   **P2P核心库:** **`go-libp2p`**
*   **CLI框架:** **`Cobra`**
*   **配置文件处理:** **`Viper`**
*   **应用层协议:** 在`libp2p`流上使用**JSON**或**Protobuf**定义控制消息，用于协商子流的创建。Protobuf在性能和类型安全上更优，但初期JSON更易于调试。

### 6. 核心功能与CLI/配置文件设计（v3.0）

#### **配置文件示例 (`config.yaml`)**

```yaml
# 信令服务器地址
signal: "/ip4/your-server.com/tcp/4001"

# ---- 连接方 (Connector) 配置 ----
# 目标监听方的Peer ID
peer: "12D3KooW..." 

# 核心: 定义一个或多个转发映射
forwards:
  # 规则一: 代理Web服务
  - local_port: 8080
    remote_target: "localhost:3000"
  
  # 规则二: 代理数据库
  - local_port: 5433
    remote_target: "localhost:5432"

  # 规则三: 代理SSH
  - local_port: 2222
    remote_target: "localhost:22"
```

#### **CLI命令设计**

```bash
# 启动信令服务器
peertunnel server --port 4332 --enable-relay

# 监听方: 启动并进入待命状态
peertunnel listen --config ./config.yaml

# 连接方: 根据配置文件中的多条规则, 建立多路复用隧道
peertunnel connect --config ./config.yaml
```

---
这份文档描绘了一个功能非常强大且实用的工具蓝图，真正解决了分布式团队协作中的核心效率问题。它将是这个项目的终极形态，具备很强的市场竞争力。