package cmd

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/47sang/peertunnel/internal/p2p"
	"github.com/libp2p/go-libp2p/p2p/protocol/circuitv2/relay"
	"github.com/spf13/cobra"
)

var serverCmd = &cobra.Command{
	Use:   "server",
	Short: "运行一个汇合服务器",
	Long:  `运行一个汇合服务器，以帮助节点发现彼此并根据需要中继流量。`,
	Run: func(cmd *cobra.Command, args []string) {
		port, _ := cmd.Flags().GetInt("port")
		enableRelay, _ := cmd.Flags().GetBool("enable-relay")
		keyFile, _ := cmd.Flags().GetString("key")

		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		privKey, err := p2p.LoadOrGeneratePrivateKey(keyFile)
		if err != nil {
			log.Fatalf("加载或生成私钥失败: %v", err)
		}

		host, err := p2p.NewHost(ctx, port, enableRelay, nil, privKey)
		if err != nil {
			log.Fatalf("创建主机失败: %v", err)
		}
		log.Printf("🌟 汇合服务器已启动")
		log.Printf("🌟 节点ID: %s", host.ID().String())
		log.Printf("🌟 正在监听: %v", host.Addrs())

		// 添加连接事件监听
		host.Network().Notify(&p2p.NetworkNotifiee{})

		if enableRelay {
			log.Println("🚀 正在启动中继服务...")
			acl := &p2p.LoggingACL{}
			_, err = relay.New(host, relay.WithACL(acl))
			if err != nil {
				log.Fatalf("实例化中继失败: %v", err)
			}
			log.Println("✅ 中继服务已启用")
			log.Println("📍 中继服务正在监听预留和连接请求...")
			log.Println("📍 将记录所有中继预留和连接活动")
		}

		// This is a dummy discovery to keep the service running
		// In a real-world scenario, you might want more complex discovery logic
		rendezvousPoint := "peertunnel-rendezvous"
		log.Printf("🔍 正在启动节点发现服务...")
		err = p2p.DiscoverPeers(ctx, host, rendezvousPoint)
		if err != nil {
			log.Fatalf("启动发现失败: %v", err)
		}

		log.Printf("🔍 在汇合点上广播自己: %s", rendezvousPoint)
		log.Printf("🔍 服务器现在可以被其他节点发现")

		// Wait for a SIGINT or SIGTERM signal
		ch := make(chan os.Signal, 1)
		signal.Notify(ch, syscall.SIGINT, syscall.SIGTERM)
		<-ch
		fmt.Println("\n收到信号，正在关闭...")
	},
}

func init() {
	rootCmd.AddCommand(serverCmd)
	serverCmd.Flags().IntP("port", "p", 4001, "监听端口")
	serverCmd.Flags().Bool("enable-relay", true, "启用中继服务")
	serverCmd.Flags().String("key", "server.key", "私钥文件的路径")
}
