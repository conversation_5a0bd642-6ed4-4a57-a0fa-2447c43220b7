package p2p

import (
	"io"
	"log"
	"net"
	"strings"
	"sync"
	"time"
)

// Proxy copies data between a p2p stream and a local TCP connection, with logging.
func Proxy(p2pConn io.ReadWriteCloser, localConn io.ReadWriteCloser) {
	var wg sync.WaitGroup
	wg.Add(2)

	sessionStart := time.Now()
	sessionID := sessionStart.Format("150405.000")

	log.Printf("🔄 [代理会话] 会话ID: %s", sessionID)
	log.Printf("🔄 [代理会话] 开始时间: %s", sessionStart.Format("2006-01-02 15:04:05"))
	log.Printf("🔄 [代理会话] 状态: 🚀 代理会话已建立，开始双向数据转发")

	// Goroutine to copy data from the local service to the p2p stream
	go func() {
		defer wg.Done()
		// Closing one connection will cause the other io.Copy to exit
		defer p2pConn.Close()
		defer localConn.Close()

		directionStart := time.Now()
		bytes, err := io.Copy(p2pConn, localConn)
		duration := time.Since(directionStart)

		if err != nil && !isIgnorableError(err) {
			log.Printf("❌ [代理数据] 会话ID: %s", sessionID)
			log.Printf("❌ [代理数据] 方向: 本地 → P2P")
			log.Printf("❌ [代理数据] 错误: %v", err)
			log.Printf("❌ [代理数据] 传输字节: %d", bytes)
			log.Printf("❌ [代理数据] 传输时长: %v", duration)
		} else {
			log.Printf("📊 [代理数据] 会话ID: %s", sessionID)
			log.Printf("📊 [代理数据] 方向: 本地 → P2P")
			log.Printf("📊 [代理数据] 传输字节: %d", bytes)
			log.Printf("📊 [代理数据] 传输时长: %v", duration)
			if duration > 0 {
				speed := float64(bytes) / duration.Seconds() / 1024 // KB/s
				log.Printf("📊 [代理数据] 平均速度: %.2f KB/s", speed)
			}
		}
	}()

	// Goroutine to copy data from the p2p stream to the local service
	go func() {
		defer wg.Done()
		defer p2pConn.Close()
		defer localConn.Close()

		directionStart := time.Now()
		bytes, err := io.Copy(localConn, p2pConn)
		duration := time.Since(directionStart)

		if err != nil && !isIgnorableError(err) {
			log.Printf("❌ [代理数据] 会话ID: %s", sessionID)
			log.Printf("❌ [代理数据] 方向: P2P → 本地")
			log.Printf("❌ [代理数据] 错误: %v", err)
			log.Printf("❌ [代理数据] 传输字节: %d", bytes)
			log.Printf("❌ [代理数据] 传输时长: %v", duration)
		} else {
			log.Printf("📊 [代理数据] 会话ID: %s", sessionID)
			log.Printf("📊 [代理数据] 方向: P2P → 本地")
			log.Printf("📊 [代理数据] 传输字节: %d", bytes)
			log.Printf("📊 [代理数据] 传输时长: %v", duration)
			if duration > 0 {
				speed := float64(bytes) / duration.Seconds() / 1024 // KB/s
				log.Printf("📊 [代理数据] 平均速度: %.2f KB/s", speed)
			}
		}
	}()

	wg.Wait()

	totalDuration := time.Since(sessionStart)
	log.Printf("🏁 [代理会话] 会话ID: %s", sessionID)
	log.Printf("🏁 [代理会话] 结束时间: %s", time.Now().Format("2006-01-02 15:04:05"))
	log.Printf("🏁 [代理会话] 总时长: %v", totalDuration)
	log.Printf("🏁 [代理会话] 状态: ✅ 代理会话已正常结束")
	log.Printf("🏁 [代理会话] ---")
}

// isIgnorableError checks for errors that are expected during connection teardown
// and can be safely ignored to prevent log spam.
func isIgnorableError(err error) bool {
	if err == io.EOF {
		return true
	}
	// Check for "use of closed network connection" error, common on graceful shutdown.
	if opErr, ok := err.(*net.OpError); ok && opErr.Err.Error() == "use of closed network connection" {
		return true
	}
	// Check for libp2p stream reset errors.
	if strings.Contains(err.Error(), "stream reset") {
		return true
	}
	return false
}
