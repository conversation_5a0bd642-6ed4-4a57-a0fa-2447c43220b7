package cmd

import (
	"context"
	"fmt"
	"log"
	"net"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/47sang/peertunnel/internal/config"
	"github.com/47sang/peertunnel/internal/p2p"
	"github.com/libp2p/go-libp2p/core/host"
	"github.com/libp2p/go-libp2p/core/network"
	"github.com/libp2p/go-libp2p/core/peer"
	"github.com/libp2p/go-libp2p/p2p/protocol/circuitv2/client"
	"github.com/multiformats/go-multiaddr"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var listenCmd = &cobra.Command{
	Use:   "listen",
	Short: "运行监听服务 (即隧道的\"服务器\"端)",
	Long:  `监听传入的p2p连接，并根据控制消息将流量转发到本地服务。`,
	Run: func(cmd *cobra.Command, args []string) {
		cfg, err := config.GetConfig()
		if err != nil {
			log.Fatalf("获取配置失败: %v", err)
		}

		signalAddr := viper.GetString("signal")
		if signalAddr == "" {
			signalAddr = cfg.Signal
		}
		if signalAddr == "" {
			log.Fatal("未提供信令服务器地址。请使用 --signal 标志或在配置文件中设置。")
		}
		keyFile, _ := cmd.Flags().GetString("key")

		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		var staticRelays []multiaddr.Multiaddr
		if signalAddr != "" {
			addr, err := multiaddr.NewMultiaddr(signalAddr)
			if err != nil {
				log.Fatalf("无效的信令服务器地址: %v", err)
			}
			staticRelays = append(staticRelays, addr)
		}

		privKey, err := p2p.LoadOrGeneratePrivateKey(keyFile)
		if err != nil {
			log.Fatalf("加载或生成私钥失败: %v", err)
		}

		h, err := p2p.NewHost(ctx, 0, false, staticRelays, privKey)
		if err != nil {
			log.Fatalf("创建主机失败: %v", err)
		}

		if err := p2p.ConnectToPeer(ctx, h, signalAddr); err != nil {
			log.Fatalf("连接到信令服务器失败: %v", err)
		} else {
			log.Printf("成功连接到信令服务器: %s", signalAddr)
		}

		// Explicitly reserve a slot on the relay
		log.Println("正在尝试通过信令服务器预留中继槽位...")
		relayAddrInfo, err := peer.AddrInfoFromP2pAddr(staticRelays[0])
		if err != nil {
			log.Fatalf("从地址获取中继信息失败: %v", err)
		}

		_, err = client.Reserve(ctx, h, *relayAddrInfo)
		if err != nil {
			log.Println("⚠️  警告：中继预留失败！")
			log.Printf("   错误: %v", err)
			log.Println("⚠️  这意味着其他节点可能无法通过中继连接到此节点。")
			log.Println("⚠️  请检查：")
			log.Println("   1. 信令服务器是否启用了中继服务 (v2 circuit relay)")
			log.Println("   2. 网络连接是否正常")
			log.Println("   3. 防火墙设置是否允许连接")
		} else {
			log.Println("✅ 中继预留成功。节点现在可以通过中继访问。")
			log.Printf("📍 中继地址: %s/p2p-circuit", staticRelays[0])
		}

		log.Printf("监听器已启动，节点ID: %s", h.ID().String())
		log.Printf("完整监听地址: %v", h.Addrs())
		log.Println("---")
		log.Println("请将节点ID分享给连接方。")
		log.Println("---")
		log.Println("📝 [日志增强] 监听器已启用详细日志记录功能:")
		log.Println("📝 [日志增强]   📥 新控制流 - 记录每个新的P2P连接详情")
		log.Println("📝 [日志增强]   🎯 转发请求 - 记录端口映射请求信息")
		log.Println("📝 [日志增强]   📡 代理流建立 - 记录代理通道创建过程")
		log.Println("📝 [日志增强]   🔌 本地服务连接 - 记录与本地服务的连接状态")
		log.Println("📝 [日志增强]   🔄 代理会话 - 记录数据传输统计和性能信息")
		log.Println("📝 [日志增强] 所有错误和连接问题都将被详细记录以便排查。")
		log.Println("---")

		// Set the stream handler for control messages
		streamHandler := func(stream network.Stream) {
			// 记录新控制流的详细连接信息
			timestamp := time.Now().Format("2006-01-02 15:04:05")
			remotePeer := stream.Conn().RemotePeer()
			protocol := stream.Protocol()

			log.Printf("📥 [新控制流] 时间: %s", timestamp)
			log.Printf("📥 [新控制流] 协议: %s", protocol)
			log.Printf("📥 [新控制流] 远程节点ID: %s", remotePeer.String())
			log.Printf("📥 [新控制流] 远程地址: %s", stream.Conn().RemoteMultiaddr().String())
			log.Printf("📥 [新控制流] 本地地址: %s", stream.Conn().LocalMultiaddr().String())
			log.Printf("📥 [新控制流] 连接状态: ✅ 新的控制流已建立")
			log.Printf("📥 [新控制流] 连接传输: %s", stream.Conn().ConnState().Transport)

			// 检查是否为中继连接
			if stream.Conn().RemoteMultiaddr().String() != "" {
				if addr := stream.Conn().RemoteMultiaddr().String(); addr != "" {
					if len(addr) > 0 && (addr[len(addr)-15:] == "/p2p-circuit" ||
						stream.Conn().RemoteMultiaddr().String() != stream.Conn().LocalMultiaddr().String()) {
						log.Printf("📥 [新控制流] 连接类型: 🔄 中继连接")
					} else {
						log.Printf("📥 [新控制流] 连接类型: 🔗 直接连接")
					}
				}
			}

			log.Printf("📥 [新控制流] 处理状态: 🚀 开始处理控制流")
			log.Printf("📥 [新控制流] ---")

			go handleControlStream(ctx, h, stream)
		}
		h.SetStreamHandler(p2p.ControlProtocolID, streamHandler)

		log.Println("监听器已就绪，等待连接。")

		// Wait for a SIGINT or SIGTERM signal
		ch := make(chan os.Signal, 1)
		signal.Notify(ch, syscall.SIGINT, syscall.SIGTERM)
		<-ch
		fmt.Println("\n收到信号，正在关闭...")
	},
}

// handleControlStream is called for each new control stream.
func handleControlStream(ctx context.Context, h host.Host, controlStream network.Stream) {
	defer controlStream.Close()

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	remotePeer := controlStream.Conn().RemotePeer()

	log.Printf("🔄 [控制流处理] 时间: %s", timestamp)
	log.Printf("🔄 [控制流处理] 远程节点: %s", remotePeer.String())
	log.Printf("🔄 [控制流处理] 状态: 📖 开始读取控制消息")

	msg, err := p2p.ReadControlMessage(controlStream)
	if err != nil {
		log.Printf("❌ [控制流处理] 时间: %s", time.Now().Format("2006-01-02 15:04:05"))
		log.Printf("❌ [控制流处理] 远程节点: %s", remotePeer.String())
		log.Printf("❌ [控制流处理] 错误: 读取控制消息失败")
		log.Printf("❌ [控制流处理] 详细错误: %v", err)
		log.Printf("❌ [控制流处理] 结果: 🚫 控制流处理中止")
		log.Printf("❌ [控制流处理] ---")
		return
	}

	log.Printf("✅ [控制流处理] 状态: 📨 控制消息读取成功")
	log.Printf("═══════════════════════════════════════════════════════════════")
	log.Printf("🎯 [新转发请求] 时间: %s", time.Now().Format("2006-01-02 15:04:05"))
	log.Printf("🎯 [新转发请求] 请求ID: %s", msg.RequestID)
	log.Printf("🎯 [新转发请求] 远程目标: %s", msg.RemoteTarget)
	log.Printf("🎯 [新转发请求] 请求来源: %s", remotePeer.String())
	log.Printf("🎯 [新转发请求] 源地址: %s", controlStream.Conn().RemoteMultiaddr().String())
	log.Printf("🎯 [新转发请求] 状态: 🔄 开始建立代理流")
	log.Printf("═══════════════════════════════════════════════════════════════")

	// Open a new data stream back to the connector
	log.Printf("📡 [代理流建立] 请求ID: %s", msg.RequestID)
	log.Printf("📡 [代理流建立] 目标节点: %s", remotePeer.String())
	log.Printf("📡 [代理流建立] 协议: %s", p2p.ProxyProtocolID)
	log.Printf("📡 [代理流建立] 状态: 🚀 正在创建代理流")

	proxyStream, err := h.NewStream(ctx, remotePeer, p2p.ProxyProtocolID)
	if err != nil {
		log.Printf("❌ [代理流建立] 时间: %s", time.Now().Format("2006-01-02 15:04:05"))
		log.Printf("❌ [代理流建立] 请求ID: %s", msg.RequestID)
		log.Printf("❌ [代理流建立] 目标节点: %s", remotePeer.String())
		log.Printf("❌ [代理流建立] 错误: 无法创建代理流")
		log.Printf("❌ [代理流建立] 详细错误: %v", err)
		log.Printf("❌ [代理流建立] 结果: 🚫 转发请求处理失败")
		log.Printf("❌ [代理流建立] ---")
		return
	}

	log.Printf("✅ [代理流建立] 状态: 🎉 代理流创建成功")
	log.Printf("✅ [代理流建立] 代理地址: %s", proxyStream.Conn().RemoteMultiaddr().String())

	// Write the request ID back to the connector so it can match the stream
	log.Printf("📝 [请求ID写入] 请求ID: %s", msg.RequestID)
	log.Printf("📝 [请求ID写入] 状态: ✍️ 正在写入请求ID到代理流")

	_, err = proxyStream.Write([]byte(msg.RequestID + "\n"))
	if err != nil {
		log.Printf("❌ [请求ID写入] 时间: %s", time.Now().Format("2006-01-02 15:04:05"))
		log.Printf("❌ [请求ID写入] 请求ID: %s", msg.RequestID)
		log.Printf("❌ [请求ID写入] 错误: 向代理流写入请求ID失败")
		log.Printf("❌ [请求ID写入] 详细错误: %v", err)
		log.Printf("❌ [请求ID写入] 结果: 🚫 代理流重置")
		proxyStream.Reset()
		return
	}

	log.Printf("✅ [请求ID写入] 状态: 📤 请求ID写入成功")

	// Connect to the local target service
	log.Printf("🔌 [本地服务连接] 请求ID: %s", msg.RequestID)
	log.Printf("🔌 [本地服务连接] 目标地址: %s", msg.RemoteTarget)
	log.Printf("🔌 [本地服务连接] 状态: 🚀 正在连接本地目标服务")

	targetConn, err := net.Dial("tcp", msg.RemoteTarget)
	if err != nil {
		log.Printf("❌ [本地服务连接] 时间: %s", time.Now().Format("2006-01-02 15:04:05"))
		log.Printf("❌ [本地服务连接] 请求ID: %s", msg.RequestID)
		log.Printf("❌ [本地服务连接] 目标地址: %s", msg.RemoteTarget)
		log.Printf("❌ [本地服务连接] 错误: 连接本地目标服务失败")
		log.Printf("❌ [本地服务连接] 详细错误: %v", err)
		log.Printf("❌ [本地服务连接] 可能原因:")
		log.Printf("   1. 目标服务未启动")
		log.Printf("   2. 端口被防火墙阻止")
		log.Printf("   3. 服务地址配置错误")
		log.Printf("   4. 服务负载过高无法接受连接")
		log.Printf("❌ [本地服务连接] 结果: 🚫 代理流重置")
		proxyStream.Reset()
		return
	}

	log.Printf("✅ [本地服务连接] 状态: 🎉 本地服务连接成功")
	log.Printf("✅ [本地服务连接] 本地地址: %s", targetConn.LocalAddr().String())
	log.Printf("✅ [本地服务连接] 远程地址: %s", targetConn.RemoteAddr().String())

	log.Printf("🚀 [代理会话启动] 请求ID: %s", msg.RequestID)
	log.Printf("🚀 [代理会话启动] P2P流: %s ← → 本地服务: %s", remotePeer.String(), msg.RemoteTarget)
	log.Printf("🚀 [代理会话启动] 状态: 📡 代理数据传输已开始")
	log.Printf("🚀 [代理会话启动] 时间: %s", time.Now().Format("2006-01-02 15:04:05"))
	log.Printf("🚀 [代理会话启动] ---")

	p2p.Proxy(proxyStream, targetConn)
}

func init() {
	rootCmd.AddCommand(listenCmd)
	listenCmd.Flags().String("key", "listener.key", "私钥文件的路径")
}
