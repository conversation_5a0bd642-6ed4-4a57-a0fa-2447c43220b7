package p2p

import (
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/libp2p/go-libp2p/core/network"
)

// StreamRequest holds a channel waiting for an incoming stream.
type StreamRequest struct {
	StreamChan chan network.Stream
	Timestamp  time.Time
}

// StreamManager keeps track of pending proxy requests.
type StreamManager struct {
	sync.RWMutex
	pendingRequests map[string]*StreamRequest
}

// NewStreamManager creates a new stream manager.
func NewStreamManager() *StreamManager {
	return &StreamManager{
		pendingRequests: make(map[string]*StreamRequest),
	}
}

// RegisterRequest creates a new request and returns its ID.
func (m *StreamManager) RegisterRequest() (string, <-chan network.Stream) {
	m.Lock()
	defer m.Unlock()

	id := uuid.New().String()
	req := &StreamRequest{
		StreamChan: make(chan network.Stream, 1),
		Timestamp:  time.Now(),
	}

	m.pendingRequests[id] = req
	return id, req.Stream<PERSON>han
}

// FulfillRequest finds a pending request and sends the stream to its channel.
func (m *StreamManager) FulfillRequest(id string, stream network.Stream) bool {
	m.Lock()
	defer m.Unlock()

	req, ok := m.pendingRequests[id]
	if !ok {
		return false
	}

	req.StreamChan <- stream
	close(req.StreamChan)
	delete(m.pendingRequests, id)
	return true
}

// Cleanup removes old, unfulfilled requests.
func (m *StreamManager) Cleanup(timeout time.Duration) {
	m.Lock()
	defer m.Unlock()

	now := time.Now()
	for id, req := range m.pendingRequests {
		if now.Sub(req.Timestamp) > timeout {
			close(req.StreamChan)
			delete(m.pendingRequests, id)
		}
	}
}
