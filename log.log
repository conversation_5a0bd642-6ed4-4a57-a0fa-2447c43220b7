 ./peertunnel server
🔑 从 server.key 加载了现有私钥
2025/06/07 01:05:32 server.go:37: 🌟 汇合服务器已启动
2025/06/07 01:05:32 server.go:38: 🌟 节点ID: 12D3KooWGRiSRZh5eJGLBzR2SCP8Addh3FctM1XgNqVs8f9enEPo
2025/06/07 01:05:32 server.go:39: 🌟 正在监听: [/ip4/127.0.0.1/tcp/4001 /ip4/**************/tcp/4001 /ip6/::1/tcp/4001]
2025/06/07 01:05:32 server.go:45: 🚀 正在启动中继服务...
2025/06/07 01:05:32 server.go:51: ✅ 中继服务已启用
2025/06/07 01:05:32 server.go:52: 📍 中继服务正在监听预留和连接请求...
2025/06/07 01:05:32 server.go:53: 📍 将记录所有中继预留和连接活动
2025/06/07 01:05:32 server.go:59: 🔍 正在启动节点发现服务...
2025/06/07 01:05:32 server.go:65: 🔍 在汇合点上广播自己: peertunnel-rendezvous
2025/06/07 01:05:32 server.go:66: 🔍 服务器现在可以被其他节点发现
2025/06/07 01:05:35 acl.go:51: 🔌 [节点连接] 时间: 2025-06-07 01:05:35
2025/06/07 01:05:35 acl.go:52: 🔌 [节点连接] 远程节点ID: 12D3KooWNJj7GjVGrskHzDUVan41gJCyiWDsxGeWPtkVEgy49KLk
2025/06/07 01:05:35 acl.go:53: 🔌 [节点连接] 远程地址: /ip4/*************/tcp/35705
2025/06/07 01:05:35 acl.go:54: 🔌 [节点连接] 本地地址: /ip4/**************/tcp/4001
2025/06/07 01:05:35 acl.go:55: 🔌 [节点连接] 状态: ✅ 节点已连接到服务器
2025/06/07 01:05:35 acl.go:56: 🔌 [节点连接] ---
2025/06/07 01:05:35 acl.go:17: 📍 [中继预留] 时间: 2025-06-07 01:05:35
2025/06/07 01:05:35 acl.go:18: 📍 [中继预留] 节点ID: 12D3KooWNJj7GjVGrskHzDUVan41gJCyiWDsxGeWPtkVEgy49KLk
2025/06/07 01:05:35 acl.go:19: 📍 [中继预留] 节点地址: /ip4/*************/tcp/35705
2025/06/07 01:05:35 acl.go:20: 📍 [中继预留] 状态: ✅ 允许预留中继槽位
2025/06/07 01:05:35 acl.go:21: 📍 [中继预留] ---
2025/06/07 01:05:54 acl.go:51: 🔌 [节点连接] 时间: 2025-06-07 01:05:54
2025/06/07 01:05:54 acl.go:52: 🔌 [节点连接] 远程节点ID: 12D3KooWQjL3AQpHJqwjv2ZnoD34R3KYV5Jv2PqY1xpEwKptiQzo
2025/06/07 01:05:54 acl.go:53: 🔌 [节点连接] 远程地址: /ip4/*************/tcp/13863
2025/06/07 01:05:54 acl.go:54: 🔌 [节点连接] 本地地址: /ip4/**************/tcp/4001
2025/06/07 01:05:54 acl.go:55: 🔌 [节点连接] 状态: ✅ 节点已连接到服务器
2025/06/07 01:05:54 acl.go:56: 🔌 [节点连接] ---
2025/06/07 01:05:54 acl.go:27: 🔗 [中继连接] 时间: 2025-06-07 01:05:54
2025/06/07 01:05:54 acl.go:28: 🔗 [中继连接] 源节点ID: 12D3KooWQjL3AQpHJqwjv2ZnoD34R3KYV5Jv2PqY1xpEwKptiQzo
2025/06/07 01:05:54 acl.go:29: 🔗 [中继连接] 源节点地址: /ip4/*************/tcp/13863
2025/06/07 01:05:54 acl.go:30: 🔗 [中继连接] 目标节点ID: 12D3KooWNJj7GjVGrskHzDUVan41gJCyiWDsxGeWPtkVEgy49KLk
2025/06/07 01:05:54 acl.go:31: 🔗 [中继连接] 状态: ✅ 允许中继连接
2025/06/07 01:05:54 acl.go:32: 🔗 [中继连接] ---
2025/06/07 01:06:11 acl.go:17: 📍 [中继预留] 时间: 2025-06-07 01:06:11
2025/06/07 01:06:11 acl.go:18: 📍 [中继预留] 节点ID: 12D3KooWQjL3AQpHJqwjv2ZnoD34R3KYV5Jv2PqY1xpEwKptiQzo
2025/06/07 01:06:11 acl.go:19: 📍 [中继预留] 节点地址: /ip4/*************/tcp/13863
2025/06/07 01:06:11 acl.go:20: 📍 [中继预留] 状态: ✅ 允许预留中继槽位
2025/06/07 01:06:11 acl.go:21: 📍 [中继预留] ---

./peertunnel listen --config ./config.yaml
🔑 从 listener.key 加载了现有私钥
已连接到节点: 12D3KooWGRiSRZh5eJGLBzR2SCP8Addh3FctM1XgNqVs8f9enEPo
2025/06/07 01:05:35 listen.go:68: 成功连接到信令服务器: /ip4/************/tcp/4001/p2p/12D3KooWGRiSRZh5eJGLBzR2SCP8Addh3FctM1XgNqVs8f9enEPo
2025/06/07 01:05:35 listen.go:72: 正在尝试通过信令服务器预留中继槽位...
2025/06/07 01:05:35 listen.go:88: ✅ 中继预留成功。节点现在可以通过中继访问。
2025/06/07 01:05:35 listen.go:89: 📍 中继地址: /ip4/************/tcp/4001/p2p/12D3KooWGRiSRZh5eJGLBzR2SCP8Addh3FctM1XgNqVs8f9enEPo/p2p-circuit
2025/06/07 01:05:35 listen.go:92: 监听器已启动，节点ID: 12D3KooWNJj7GjVGrskHzDUVan41gJCyiWDsxGeWPtkVEgy49KLk
2025/06/07 01:05:35 listen.go:93: 完整监听地址: [/ip4/*********/tcp/35705 /ip4/127.0.0.1/tcp/35705 /ip6/::1/tcp/41057]
2025/06/07 01:05:35 listen.go:94: ---
2025/06/07 01:05:35 listen.go:95: 请将节点ID分享给连接方。
2025/06/07 01:05:35 listen.go:96: ---
2025/06/07 01:05:35 listen.go:97: 📝 [日志增强] 监听器已启用详细日志记录功能:
2025/06/07 01:05:35 listen.go:98: 📝 [日志增强]   📥 新控制流 - 记录每个新的P2P连接详情
2025/06/07 01:05:35 listen.go:99: 📝 [日志增强]   🎯 转发请求 - 记录端口映射请求信息
2025/06/07 01:05:35 listen.go:100: 📝 [日志增强]   📡 代理流建立 - 记录代理通道创建过程
2025/06/07 01:05:35 listen.go:101: 📝 [日志增强]   🔌 本地服务连接 - 记录与本地服务的连接状态
2025/06/07 01:05:35 listen.go:102: 📝 [日志增强]   🔄 代理会话 - 记录数据传输统计和性能信息
2025/06/07 01:05:35 listen.go:103: 📝 [日志增强] 所有错误和连接问题都将被详细记录以便排查。
2025/06/07 01:05:35 listen.go:104: ---
2025/06/07 01:05:35 listen.go:140: 监听器已就绪，等待连接。


 ./peertunnel connect --config ./config.yaml
2025/06/07 01:05:54 connect.go:65: 连接器已启动，节点ID: 12D3KooWQjL3AQpHJqwjv2ZnoD34R3KYV5Jv2PqY1xpEwKptiQzo
已连接到节点: 12D3KooWGRiSRZh5eJGLBzR2SCP8Addh3FctM1XgNqVs8f9enEPo
2025/06/07 01:05:54 connect.go:81: 尝试通过中继连接到监听器: 12D3KooWNJj7GjVGrskHzDUVan41gJCyiWDsxGeWPtkVEgy49KLk
2025/06/07 01:05:54 connect.go:82: 中继地址: /ip4/************/tcp/4001/p2p/12D3KooWGRiSRZh5eJGLBzR2SCP8Addh3FctM1XgNqVs8f9enEPo/p2p-circuit/p2p/12D3KooWNJj7GjVGrskHzDUVan41gJCyiWDsxGeWPtkVEgy49KLk
2025/06/07 01:05:54 connect.go:111: 已与监听器建立基础P2P连接，等待本地流量触发转发... PeerID: 12D3KooWNJj7GjVGrskHzDUVan41gJCyiWDsxGeWPtkVEgy49KLk
2025/06/07 01:05:54 connect.go:170: 正在 127.0.0.1:11668 上监听，转发到远程 127.0.0.1:3306
2025/06/07 01:05:54 connect.go:170: 正在 127.0.0.1:11667 上监听，转发到远程 127.0.0.1:22
2025/06/07 01:05:54 connect.go:170: 正在 127.0.0.1:11666 上监听，转发到远程 127.0.0.1:902
2025/06/07 01:06:03 connect.go:187: 为 127.0.0.1:11667 建立新本地连接
2025/06/07 01:06:11 connect.go:204: [ID: 47b3bc22-1e98-4e65-aa4c-f28f4f35ccd8] 打开控制流失败 (尝试 1/3): failed to open stream: context deadline exceeded
2025/06/07 01:06:21 connect.go:204: [ID: 47b3bc22-1e98-4e65-aa4c-f28f4f35ccd8] 打开控制流失败 (尝试 2/3): failed to open stream: context deadline exceeded
2025/06/07 01:06:33 connect.go:204: [ID: 47b3bc22-1e98-4e65-aa4c-f28f4f35ccd8] 打开控制流失败 (尝试 3/3): failed to open stream: context deadline exceeded
2025/06/07 01:06:39 connect.go:215: [ID: 47b3bc22-1e98-4e65-aa4c-f28f4f35ccd8] 打开控制流最终失败: failed to open stream: context deadline exceeded

