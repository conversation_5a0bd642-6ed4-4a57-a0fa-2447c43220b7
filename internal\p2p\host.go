package p2p

import (
	"context"
	"fmt"

	"github.com/libp2p/go-libp2p"
	dht "github.com/libp2p/go-libp2p-kad-dht"
	"github.com/libp2p/go-libp2p/core/crypto"
	"github.com/libp2p/go-libp2p/core/host"
	"github.com/libp2p/go-libp2p/core/peer"
	"github.com/libp2p/go-libp2p/p2p/discovery/routing"
	dutil "github.com/libp2p/go-libp2p/p2p/discovery/util"
	"github.com/libp2p/go-libp2p/p2p/host/autorelay"
	"github.com/multiformats/go-multiaddr"
)

// NewHost creates a new libp2p host.
func NewHost(ctx context.Context, listenPort int, isRelayServer bool, staticRelays []multiaddr.Multiaddr, privKey crypto.PrivKey) (host.Host, error) {
	opts := []libp2p.Option{
		libp2p.ListenAddrStrings(
			fmt.Sprintf("/ip4/0.0.0.0/tcp/%d", listenPort),
			fmt.Sprintf("/ip6/::/tcp/%d", listenPort),
		),
		libp2p.EnableNATService(),
		libp2p.EnableHolePunching(),
	}

	if privKey != nil {
		opts = append(opts, libp2p.Identity(privKey))
	}

	if isRelayServer {
		opts = append(opts, libp2p.EnableRelayService())
	} else {
		opts = append(opts, libp2p.EnableRelay())
		if len(staticRelays) > 0 {
			static, err := peer.AddrInfosFromP2pAddrs(staticRelays...)
			if err != nil {
				return nil, fmt.Errorf("无效的静态中继地址: %w", err)
			}
			opts = append(opts, libp2p.EnableAutoRelay(autorelay.WithStaticRelays(static)))
		} else {
			opts = append(opts, libp2p.EnableAutoRelay())
		}
	}

	h, err := libp2p.New(opts...)
	if err != nil {
		return nil, fmt.Errorf("创建libp2p主机失败: %w", err)
	}

	return h, nil
}

// DiscoverPeers uses the DHT to find peers.
func DiscoverPeers(ctx context.Context, h host.Host, rendezvous string) error {
	kademliaDHT, err := dht.New(ctx, h)
	if err != nil {
		return fmt.Errorf("创建DHT失败: %w", err)
	}

	if err = kademliaDHT.Bootstrap(ctx); err != nil {
		return fmt.Errorf("引导DHT失败: %w", err)
	}

	routingDiscovery := routing.NewRoutingDiscovery(kademliaDHT)
	dutil.Advertise(ctx, routingDiscovery, rendezvous)

	return nil
}

// ConnectToPeer connects to a peer given its multiaddress.
func ConnectToPeer(ctx context.Context, h host.Host, peerAddr string) error {
	addr, err := multiaddr.NewMultiaddr(peerAddr)
	if err != nil {
		return fmt.Errorf("无效的节点地址: %w", err)
	}

	peerInfo, err := peer.AddrInfoFromP2pAddr(addr)
	if err != nil {
		return fmt.Errorf("获取节点信息失败: %w", err)
	}

	if err := h.Connect(ctx, *peerInfo); err != nil {
		return fmt.Errorf("连接到节点失败: %w", err)
	}

	fmt.Printf("已连接到节点: %s\n", peerInfo.ID.String())
	return nil
}
